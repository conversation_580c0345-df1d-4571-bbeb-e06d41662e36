"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "studqj",
  setup(__props) {
    const closeView = () => {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    };
    common_vendor.onShareAppMessage((options) => {
      const webViewUrl = options.webViewUrl;
      const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;
      return {
        path: sharePath
      };
    });
    common_vendor.onShareTimeline(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$1,
        b: common_vendor.o(($event) => closeView())
      };
    };
  }
};
_sfc_main.__runtimeHooks = 6;
wx.createPage(_sfc_main);
