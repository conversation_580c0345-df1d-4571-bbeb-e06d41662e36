<template>
	<web-view :src="webviewUrl">
		<cover-view class="close-view" @click="closeView()">
			<cover-image class="close-icon" src="/static/icon/public/home.png"></cover-image>
		</cover-view>
	</web-view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		watch,
		onUnmounted
	} from "vue";
	import {
		getCurrentInstance
	} from "vue";
	import {
		onHide,
		onUnload,
		onBackPress,
		onShareAppMessage,
		onShareTimeline,
	} from "@dcloudio/uni-app";

	// 获取当前实例，用于获取页面传递过来的参数
	const {
		proxy
	} = getCurrentInstance();

	// 用于存储传递过来的webview链接，初始化为空字符串
	const webviewUrl = ref("");

	// 在组件挂载后获取传递过来的参数（假设是从上个页面通过路由传参的方式传递过来的）
	onMounted(() => {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		const options = currentPage.options;
		if (options.webviewUrl) {
			webviewUrl.value = decodeURIComponent(options.webviewUrl);
		}
	});
	// 针对电子通行证场景，退出该页面后自动返回小程序首页
	onHide(() => {
		uni.reLaunch({
			url: '/pages/index/index'
		});
	});
	onUnload(() => {});
	onBackPress((options) => {});
	const closeView = () => {
		uni.reLaunch({
			url: "/pages/index/index",
		});
	};
	onShareAppMessage((options) => {
		// 获取 webViewUrl
		const webViewUrl = options.webViewUrl;
		// 构建携带参数的路径
		const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(
		webViewUrl
	)}`;
		return {
			path: sharePath,
		};
	});
	onShareTimeline(() => {});
</script>

<style>
	.close-view {
		background-color: #616161;
		border-radius: 50%;
		position: fixed;
		z-index: 99999;
		bottom: 19vh;
		right: 30px;
		visibility: visible !important;
		padding: 5px;
	}

	.close-icon {
		width: 30px;
		height: 30px;
	}
</style>