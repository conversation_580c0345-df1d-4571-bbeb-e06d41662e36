<template>
	<!-- 一卡通付款码 -->
	<web-view
		src="https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12">
		<cover-view class="close-view" @click="closeView()">
			<cover-image class="close-icon" src="/static/icon/public/home.png"></cover-image>
		</cover-view>
	</web-view>
</template>

<script setup>
	import {
		onShareAppMessage,
		onShareTimeline
	} from '@dcloudio/uni-app'
	const closeView = () => {
		uni.reLaunch({
			url: '/pages/index/index'
		})
	}
	onShareAppMessage((options) => {
		// 获取 webViewUrl
		const webViewUrl =
			"https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12";
		// 构建携带参数的路径
		const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;
		return {
			path: sharePath,
		}
	})
	onShareTimeline(() => {})
</script>

<style>
	.close-view {
		background-color: #616161;
		border-radius: 50%;
		position: fixed;
		z-index: 99999;
		bottom: 19vh;
		right: 30px;
		visibility: visible !important;
		padding: 5px;
	}

	.close-icon {
		width: 30px;
		height: 30px;
	}
</style>