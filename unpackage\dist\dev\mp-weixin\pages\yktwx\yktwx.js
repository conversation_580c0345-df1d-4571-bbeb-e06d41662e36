"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "yktwx",
  setup(__props) {
    const closeView = () => {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    };
    common_vendor.onShareAppMessage((options) => {
      const webViewUrl = "https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12";
      const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;
      return {
        path: sharePath
      };
    });
    common_vendor.onShareTimeline(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$1,
        b: common_vendor.o(($event) => closeView())
      };
    };
  }
};
_sfc_main.__runtimeHooks = 6;
wx.createPage(_sfc_main);
