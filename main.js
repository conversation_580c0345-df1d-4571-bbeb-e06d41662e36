import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
// 用于创建一个服务端渲染（Server-Side Rendering，SSR）相关的 Vue 应用实例，是 Vue 3 中创建应用的标准方式，替代了 Vue 2 中的 new Vue() 构造方式。
import { createSSRApp } from 'vue'
import * as Pinia from 'pinia';
export function createApp() {
  const app = createSSRApp(App);
  app.use(Pinia.createPinia());
  return {
    app,
	Pinia,
  }
}
// #endif