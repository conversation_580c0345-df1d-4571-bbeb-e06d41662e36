"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/microservices/microservices.js";
  "./pages/serviceWebView/serviceWebView.js";
  "./pages/telephone/telephone.js";
  "./pages/businessForm/businessForm.js";
  "./pages/henaumap/henaumap.js";
  "./pages/studqj/studqj.js";
  "./pages/sysxxh/sysxxh.js";
  "./pages/oauthAppCNPRS/oauthAppCNPRS.js";
  "./pages/myMessage/myMessage.js";
  "./pages/yktwx/yktwx.js";
  "./pages/ac/ac.js";
  "./pages/visitorReservation/visitorReservation.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("App Launch");
  },
  onShow: function() {
    console.log("App Show");
  },
  onHide: function() {
    console.log("App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(common_vendor.createPinia());
  return {
    app,
    Pinia: common_vendor.Pinia
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
