{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "农大微门户"
			}
		},
		{
			"path": "pages/microservices/microservices",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/serviceWebView/serviceWebView",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/telephone/telephone",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/businessForm/businessForm",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/henaumap/henaumap",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path" : "pages/studqj/studqj",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/sysxxh/sysxxh",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/oauthAppCNPRS/oauthAppCNPRS",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/myMessage/myMessage",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/yktwx/yktwx",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/ac/ac",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		},
		{
			"path" : "pages/visitorReservation/visitorReservation",
			"style" : 
			{
				"navigationBarTitleText" : ""
			}
		}
	],
	"globalStyle": {
		// 设置全局导航栏文字的颜色为黑色，适用于应用内所有未单独设置该样式的页面导航栏文字
		"navigationBarTextStyle": "black",
		// 定义全局默认的导航栏标题文字内容，如果页面自身没有单独设置导航栏标题文字，就会显示这个默认的“uni-app”
		"navigationBarTitleText": "农大微门户",
		// 设置全局导航栏的背景颜色为浅灰色（#F8F8F8这个十六进制颜色值对应的颜色效果）
		"navigationBarBackgroundColor": "#F8F8F8",
		// 设置应用整体的背景颜色为浅灰色，也就是页面未被其他内容覆盖的部分的背景颜色
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}